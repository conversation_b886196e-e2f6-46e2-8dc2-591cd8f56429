/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { BaseProvider } from './base-provider.js';
import {
  ProviderConfig,
  ProviderType,
  ProviderError,
  ProviderErrorType,
  DEFAULT_MODELS,
} from './types.js';
import { EnvConfig } from '../config/env-config.js';

/**
 * Factory for creating provider instances
 */
export class ProviderFactory {
  private static providers: Map<
    ProviderType,
    new (config: ProviderConfig) => BaseProvider
  > = new Map();

  /**
   * Register a provider class
   */
  static registerProvider(
    type: ProviderType,
    providerClass: new (config: ProviderConfig) => BaseProvider,
  ): void {
    this.providers.set(type, providerClass);
  }

  /**
   * Create a provider instance
   */
  static async createProvider(config: ProviderConfig): Promise<BaseProvider> {
    const ProviderClass = this.providers.get(config.provider);

    if (!ProviderClass) {
      throw new ProviderError(
        ProviderErrorType.INVALID_REQUEST,
        `Provider ${config.provider} is not registered. Available providers: ${Array.from(this.providers.keys()).join(', ')}`,
      );
    }

    // Set default model if not specified
    if (!config.model) {
      config.model = DEFAULT_MODELS[config.provider];
    }

    return new ProviderClass(config);
  }

  /**
   * Get available provider types
   */
  static getAvailableProviders(): ProviderType[] {
    return Array.from(this.providers.keys());
  }

  /**
   * Check if a provider is available
   */
  static isProviderAvailable(type: ProviderType): boolean {
    return this.providers.has(type);
  }

  /**
   * Create provider config from environment and options
   */
  static createProviderConfig(
    provider: ProviderType,
    model?: string,
    options: Partial<ProviderConfig> = {},
  ): ProviderConfig {
    const config: ProviderConfig = {
      provider,
      model: model || DEFAULT_MODELS[provider],
      ...options,
    };

    // Set API key from environment if not provided
    if (!config.apiKey) {
      config.apiKey = this.getApiKeyFromEnvironment(provider);
    }

    return config;
  }

  /**
   * Get API key from environment variables using EnvConfig
   */
  private static getApiKeyFromEnvironment(
    provider: ProviderType,
  ): string | undefined {
    const envConfig = EnvConfig.getS647Config();

    switch (provider) {
      case ProviderType.GEMINI:
        return envConfig.geminiApiKey;
      case ProviderType.OPENAI:
        return envConfig.openaiApiKey;
      case ProviderType.ANTHROPIC:
        return envConfig.anthropicApiKey;
      case ProviderType.COHERE:
        return envConfig.cohereApiKey;
      case ProviderType.MISTRAL:
        return envConfig.mistralApiKey;
      case ProviderType.OPENAI_COMPATIBLE:
        return envConfig.openaiApiKey; // Use OpenAI key as fallback
      default:
        return undefined;
    }
  }

  /**
   * Validate provider configuration
   */
  static validateProviderConfig(config: ProviderConfig): void {
    if (!config.provider) {
      throw new ProviderError(
        ProviderErrorType.INVALID_REQUEST,
        'Provider type is required',
      );
    }

    if (!Object.values(ProviderType).includes(config.provider)) {
      throw new ProviderError(
        ProviderErrorType.INVALID_REQUEST,
        `Invalid provider type: ${config.provider}. Available providers: ${Object.values(ProviderType).join(', ')}`,
      );
    }

    if (!this.isProviderAvailable(config.provider)) {
      throw new ProviderError(
        ProviderErrorType.INVALID_REQUEST,
        `Provider ${config.provider} is not available. Make sure it's properly registered.`,
      );
    }
  }

  /**
   * Auto-detect provider from model name
   */
  static detectProviderFromModel(model: string): ProviderType | null {
    // Gemini models
    if (model.startsWith('gemini-')) {
      return ProviderType.GEMINI;
    }

    // OpenAI models
    if (
      model.startsWith('gpt-') ||
      model.includes('davinci') ||
      model.includes('curie')
    ) {
      return ProviderType.OPENAI;
    }

    // Anthropic models
    if (model.startsWith('claude-')) {
      return ProviderType.ANTHROPIC;
    }

    // Cohere models
    if (model.startsWith('command-')) {
      return ProviderType.COHERE;
    }

    // Mistral models
    if (model.startsWith('mistral-') || model.startsWith('mixtral-')) {
      return ProviderType.MISTRAL;
    }

    return null;
  }

  /**
   * Get recommended provider based on requirements
   */
  static getRecommendedProvider(requirements: {
    needsImages?: boolean;
    needsLargeContext?: boolean;
    preferFast?: boolean;
    preferCheap?: boolean;
  }): ProviderType {
    const { needsImages, needsLargeContext, preferFast, preferCheap } =
      requirements;

    // If needs large context, prefer Gemini
    if (needsLargeContext) {
      return ProviderType.GEMINI;
    }

    // If needs images, prefer GPT-4 Turbo or Gemini
    if (needsImages) {
      return ProviderType.OPENAI; // GPT-4 Turbo has good image support
    }

    // If prefers fast responses, prefer Gemini Flash or GPT-3.5
    if (preferFast) {
      return ProviderType.GEMINI; // Gemini Flash is very fast
    }

    // If prefers cheap, prefer GPT-3.5 or Cohere
    if (preferCheap) {
      return ProviderType.COHERE;
    }

    // Default to Gemini for general use
    return ProviderType.GEMINI;
  }

  /**
   * Initialize all available providers
   */
  static async initializeProviders(): Promise<void> {
    // Providers will register themselves when imported
    // For now, we'll manually register available providers
    // OpenAI provider is registered in its own file
    // Other providers will be added as they are implemented
  }
}
