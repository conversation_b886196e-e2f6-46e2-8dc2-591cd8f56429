/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { Mistral } from '@mistralai/mistralai';
import { BaseProvider } from './base-provider.js';
import {
  ProviderConfig,
  ProviderResponse,
  ProviderStreamChunk,
  GenerateContentRequest,
  ProviderCapabilities,
  ProviderMessage,
  ProviderError,
  ProviderErrorType,
  ModelInfo,
  PROVIDER_MODELS,
  ProviderType,
} from './types.js';
import { ProviderFactory } from './provider-factory.js';

/**
 * Mistral provider implementation
 */
export class MistralProvider extends BaseProvider {
  private client: Mistral;

  constructor(config: ProviderConfig) {
    super(config);

    this.client = new Mistral({
      apiKey: config.apiKey,
    });
  }

  protected requiresApiKey(): boolean {
    return true;
  }

  getCapabilities(): ProviderCapabilities {
    return {
      supportsStreaming: true,
      supportsTools: this.config.model.includes('large'), // Only large models support tools
      supportsImages: false, // Mistral doesn't support images yet
      supportsSystemMessages: true,
      maxContextLength: this.getModelContextLength(),
      supportedModels: this.getSupportedModels().map((m) => m.id),
    };
  }

  getSupportedModels(): ModelInfo[] {
    return PROVIDER_MODELS[ProviderType.MISTRAL];
  }

  private getModelContextLength(): number {
    const model = this.getSupportedModels().find(
      (m) => m.id === this.config.model,
    );
    return model?.contextLength || 32000;
  }

  async generateContent(
    request: GenerateContentRequest,
  ): Promise<ProviderResponse> {
    return this.withRetry(async () => {
      try {
        const messages = this.convertMessages(request.messages);

        const response = await this.client.chat.complete({
          model: request.model || this.config.model,
          messages,
          maxTokens: this.normalizeMaxTokens(
            request.maxTokens || this.config.maxTokens,
          ),
          temperature: this.normalizeTemperature(
            request.temperature || this.config.temperature,
          ),
          topP: request.topP || this.config.topP,
          stop: request.stop,
          stream: false,
        });

        return this.convertResponse(response);
      } catch (error) {
        throw this.handleMistralError(error);
      }
    });
  }

  async generateContentStream(
    request: GenerateContentRequest,
  ): Promise<AsyncGenerator<ProviderStreamChunk>> {
    return this.withRetry(async () => {
      try {
        const messages = this.convertMessages(request.messages);

        const stream = await this.client.chat.stream({
          model: request.model || this.config.model,
          messages,
          maxTokens: this.normalizeMaxTokens(
            request.maxTokens || this.config.maxTokens,
          ),
          temperature: this.normalizeTemperature(
            request.temperature || this.config.temperature,
          ),
          topP: request.topP || this.config.topP,
          stop: request.stop,
        });

        return this.convertStreamResponse(stream);
      } catch (error) {
        throw this.handleMistralError(error);
      }
    });
  }

  async countTokens(content: string): Promise<number> {
    // Mistral doesn't have a direct token counting API
    // Use a rough estimation: ~4 characters per token
    return Math.ceil(content.length / 4);
  }

  /**
   * Convert internal messages to Mistral format
   */
  private convertMessages(
    messages: ProviderMessage[],
  ): Array<{ role: string; content: string }> {
    return messages.map((msg) => {
      const content =
        typeof msg.content === 'string'
          ? msg.content
          : msg.content
              .map((part) => ('text' in part ? part.text || '' : ''))
              .join('');

      return {
        role: msg.role,
        content,
      };
    });
  }

  /**
   * Convert Mistral response to internal format
   */
  private convertResponse(response: {
    choices?: Array<{
      message?: { content?: string };
      finishReason?: string | null;
    }>;
    usage?: {
      promptTokens?: number;
      completionTokens?: number;
      totalTokens?: number;
    };
    model?: string;
  }): ProviderResponse {
    const choice = response.choices?.[0];
    const content = choice?.message?.content || '';

    return {
      content,
      usage: response.usage
        ? {
            promptTokens: response.usage.promptTokens || 0,
            completionTokens: response.usage.completionTokens || 0,
            totalTokens: response.usage.totalTokens || 0,
          }
        : undefined,
      finishReason: this.convertFinishReason(choice?.finishReason),
      model: response.model,
    };
  }

  /**
   * Convert Mistral stream response to internal format
   */
  private async *convertStreamResponse(
    stream: AsyncIterable<{
      choices?: Array<{
        delta?: { content?: string };
        finishReason?: string | null;
      }>;
      usage?: {
        promptTokens?: number;
        completionTokens?: number;
        totalTokens?: number;
      };
    }>,
  ): AsyncGenerator<ProviderStreamChunk> {
    let fullContent = '';

    for await (const chunk of stream) {
      const choice = chunk.choices?.[0];
      if (!choice) continue;

      const delta = choice.delta?.content || '';
      fullContent += delta;

      yield {
        content: fullContent,
        delta,
        usage: chunk.usage
          ? {
              promptTokens: chunk.usage.promptTokens || 0,
              completionTokens: chunk.usage.completionTokens || 0,
              totalTokens: chunk.usage.totalTokens || 0,
            }
          : undefined,
        finishReason: this.convertFinishReason(choice.finishReason),
      };
    }
  }

  /**
   * Convert Mistral finish reason to internal format
   */
  private convertFinishReason(
    finishReason: string | null,
  ): 'stop' | 'length' | 'content_filter' | 'tool_calls' | undefined {
    switch (finishReason) {
      case 'stop':
        return 'stop';
      case 'length':
        return 'length';
      case 'model_length':
        return 'length';
      case 'tool_calls':
        return 'tool_calls';
      default:
        return undefined;
    }
  }

  /**
   * Handle Mistral-specific errors
   */
  private handleMistralError(error: unknown): ProviderError {
    const message = error.message || error.toString();
    const statusCode = error.statusCode || error.status;

    if (
      statusCode === 401 ||
      message.includes('unauthorized') ||
      message.includes('invalid api key')
    ) {
      return new ProviderError(
        ProviderErrorType.AUTHENTICATION,
        'Invalid Mistral API key',
        statusCode,
      );
    }

    if (
      statusCode === 429 ||
      message.includes('rate limit') ||
      message.includes('too many requests')
    ) {
      return new ProviderError(
        ProviderErrorType.RATE_LIMIT,
        'Mistral rate limit exceeded',
        statusCode,
        this.extractRetryAfter(error),
      );
    }

    if (
      statusCode === 402 ||
      message.includes('quota') ||
      message.includes('billing')
    ) {
      return new ProviderError(
        ProviderErrorType.QUOTA_EXCEEDED,
        'Mistral quota exceeded',
        statusCode,
      );
    }

    if (statusCode === 400 || message.includes('invalid request')) {
      return new ProviderError(
        ProviderErrorType.INVALID_REQUEST,
        `Mistral API error: ${message}`,
        statusCode,
      );
    }

    if (statusCode === 404 || message.includes('not found')) {
      return new ProviderError(
        ProviderErrorType.MODEL_NOT_FOUND,
        `Mistral model not found: ${message}`,
        statusCode,
      );
    }

    return this.handleError(error);
  }
}

// Register the provider
ProviderFactory.registerProvider(ProviderType.MISTRAL, MistralProvider);
