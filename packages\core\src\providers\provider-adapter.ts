/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import {
  CountTokensResponse,
  GenerateContentResponse,
  GenerateContentParameters,
  CountTokensParameters,
  EmbedContentResponse,
  EmbedContentParameters,
  Content,
} from '@google/genai';
import { ContentGenerator } from '../core/contentGenerator.js';
import { BaseProvider } from './base-provider.js';
import { ProviderMessage } from './types.js';
import { UserTierId } from '../code_assist/types.js';

/**
 * Adapter that bridges the new provider system with the existing ContentGenerator interface
 */
export class ProviderAdapter implements ContentGenerator {
  private provider: BaseProvider;
  userTier?: UserTierId;

  constructor(provider: BaseProvider) {
    this.provider = provider;
  }

  async generateContent(
    request: GenerateContentParameters,
  ): Promise<GenerateContentResponse> {
    // Convert Gemini format to provider format
    const contents = this.normalizeContents(request.contents);
    const messages = this.convertContentsToMessages(contents);

    const providerRequest = {
      messages,
      model: request.model || this.provider.getModel(),
      maxTokens: request.config?.maxOutputTokens,
      temperature: request.config?.temperature,
      topP: request.config?.topP,
      stop: request.config?.stopSequences,
      // tools: request.tools, // TODO: Add tools support
    };

    const response = await this.provider.generateContent(providerRequest);

    // Convert provider response back to Gemini format
    return this.convertProviderResponseToGemini(response);
  }

  async generateContentStream(
    request: GenerateContentParameters,
  ): Promise<AsyncGenerator<GenerateContentResponse>> {
    // Convert Gemini format to provider format
    const contents = this.normalizeContents(request.contents);
    const messages = this.convertContentsToMessages(contents);

    const providerRequest = {
      messages,
      model: request.model || this.provider.getModel(),
      maxTokens: request.config?.maxOutputTokens,
      temperature: request.config?.temperature,
      topP: request.config?.topP,
      stop: request.config?.stopSequences,
      // tools: request.tools, // TODO: Add tools support
      stream: true,
    };

    const streamResponse =
      await this.provider.generateContentStream(providerRequest);

    // Convert provider stream to Gemini format
    return this.convertProviderStreamToGemini(streamResponse);
  }

  async countTokens(
    request: CountTokensParameters,
  ): Promise<CountTokensResponse> {
    // Convert contents to string for token counting
    const contents = this.normalizeContents(request.contents);
    const text = this.convertContentsToText(contents);
    const totalTokens = await this.provider.countTokens(text);

    return {
      totalTokens,
    };
  }

  async embedContent(
    _request: EmbedContentParameters,
  ): Promise<EmbedContentResponse> {
    // For now, throw an error as embedding is not supported by all providers
    // This could be implemented later with provider-specific embedding support
    throw new Error('Embedding is not supported by the current provider');
  }

  /**
   * Normalize contents to Content[] format
   */
  private normalizeContents(contents: string | Content | Content[]): Content[] {
    if (typeof contents === 'string') {
      return [{ role: 'user', parts: [{ text: contents }] }];
    }
    if (Array.isArray(contents)) {
      return contents.map((content) => {
        if (typeof content === 'string') {
          return { role: 'user', parts: [{ text: content }] };
        }
        return content as Content;
      });
    }
    return [contents as Content];
  }

  /**
   * Convert Gemini Content[] to ProviderMessage[]
   */
  private convertContentsToMessages(contents: Content[]): ProviderMessage[] {
    return contents.map((content) => {
      const role =
        content.role === 'user'
          ? 'user'
          : content.role === 'model'
            ? 'assistant'
            : 'system';

      const parts = content.parts || [];

      // If parts contain only text, convert to string
      if (parts.every((part) => 'text' in part)) {
        const text = parts
          .map((part) => ('text' in part ? part.text || '' : ''))
          .join('');
        return {
          role,
          content: text,
        };
      }

      // Otherwise, keep as Part[]
      return {
        role,
        content: parts,
      };
    });
  }

  /**
   * Convert Contents to plain text for token counting
   */
  private convertContentsToText(contents: Content[]): string {
    return contents
      .map((content) =>
        (content.parts || [])
          .map((part) =>
            'text' in part ? part.text || '' : '[Non-text content]',
          )
          .join(''),
      )
      .join('\n');
  }

  /**
   * Convert provider response to Gemini format
   */
  private convertProviderResponseToGemini(
    response: ProviderResponse,
  ): GenerateContentResponse {
    return {
      candidates: [
        {
          content: {
            parts: [{ text: response.content }],
            role: 'model',
          },
          finishReason: this.convertFinishReason(response.finishReason),
          index: 0,
        },
      ],
      usageMetadata: response.usage
        ? {
            promptTokenCount: response.usage.promptTokens,
            candidatesTokenCount: response.usage.completionTokens,
            totalTokenCount: response.usage.totalTokens,
          }
        : undefined,
    } as GenerateContentResponse;
  }

  /**
   * Convert provider stream to Gemini format
   */
  private async *convertProviderStreamToGemini(
    stream: AsyncGenerator<ProviderStreamChunk>,
  ): AsyncGenerator<GenerateContentResponse> {
    for await (const chunk of stream) {
      yield {
        candidates: [
          {
            content: {
              parts: [{ text: chunk.delta }],
              role: 'model',
            },
            finishReason: this.convertFinishReason(chunk.finishReason),
            index: 0,
          },
        ],
        usageMetadata: chunk.usage
          ? {
              promptTokenCount: chunk.usage.promptTokens,
              candidatesTokenCount: chunk.usage.completionTokens,
              totalTokenCount: chunk.usage.totalTokens,
            }
          : undefined,
      } as GenerateContentResponse;
    }
  }

  /**
   * Convert provider finish reason to Gemini format
   */
  private convertFinishReason(finishReason?: string): string | undefined {
    switch (finishReason) {
      case 'stop':
        return 'STOP';
      case 'length':
        return 'MAX_TOKENS';
      case 'content_filter':
        return 'SAFETY';
      case 'tool_calls':
        return 'STOP'; // Gemini doesn't have a specific tool_calls finish reason
      default:
        return undefined;
    }
  }

  /**
   * Get the underlying provider
   */
  getProvider(): BaseProvider {
    return this.provider;
  }

  /**
   * Get provider type
   */
  getProviderType(): string {
    return this.provider.getProviderType();
  }

  /**
   * Check if provider supports a feature
   */
  supportsFeature(
    feature: 'streaming' | 'tools' | 'images' | 'system_messages',
  ): boolean {
    const capabilities = this.provider.getCapabilities();

    switch (feature) {
      case 'streaming':
        return capabilities.supportsStreaming;
      case 'tools':
        return capabilities.supportsTools;
      case 'images':
        return capabilities.supportsImages;
      case 'system_messages':
        return capabilities.supportsSystemMessages;
      default:
        return false;
    }
  }
}
