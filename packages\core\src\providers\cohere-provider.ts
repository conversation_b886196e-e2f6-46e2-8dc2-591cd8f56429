/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { CohereClientV2 } from 'cohere-ai';
import { BaseProvider } from './base-provider.js';
import {
  ProviderConfig,
  ProviderResponse,
  ProviderStreamChunk,
  GenerateContentRequest,
  ProviderCapabilities,
  ProviderMessage,
  ProviderError,
  ProviderErrorType,
  ModelInfo,
  PROVIDER_MODELS,
  ProviderType,
} from './types.js';
import { ProviderFactory } from './provider-factory.js';

/**
 * Cohere provider implementation
 */
export class CohereProvider extends BaseProvider {
  private client: CohereClientV2;

  constructor(config: ProviderConfig) {
    super(config);

    this.client = new CohereClientV2({
      token: config.apiKey,
    });
  }

  protected requiresApiKey(): boolean {
    return true;
  }

  getCapabilities(): ProviderCapabilities {
    return {
      supportsStreaming: true,
      supportsTools: true,
      supportsImages: false, // Cohere doesn't support images yet
      supportsSystemMessages: true,
      maxContextLength: this.getModelContextLength(),
      supportedModels: this.getSupportedModels().map((m) => m.id),
    };
  }

  getSupportedModels(): ModelInfo[] {
    return PROVIDER_MODELS[ProviderType.COHERE];
  }

  private getModelContextLength(): number {
    const model = this.getSupportedModels().find(
      (m) => m.id === this.config.model,
    );
    return model?.contextLength || 128000;
  }

  async generateContent(
    request: GenerateContentRequest,
  ): Promise<ProviderResponse> {
    return this.withRetry(async () => {
      try {
        const messages = this.convertMessagesV2(request.messages);

        const response = await this.client.chat({
          model: request.model || this.config.model,
          messages,
          maxTokens: this.normalizeMaxTokens(
            request.maxTokens || this.config.maxTokens,
          ),
          temperature: this.normalizeTemperature(
            request.temperature || this.config.temperature,
          ),
          p: request.topP || this.config.topP,
          stopSequences: request.stop,
        });

        return this.convertResponse(response as any);
      } catch (error) {
        throw this.handleCohereError(error);
      }
    });
  }

  async generateContentStream(
    request: GenerateContentRequest,
  ): Promise<AsyncGenerator<ProviderStreamChunk>> {
    return this.withRetry(async () => {
      try {
        const messages = this.convertMessagesV2(request.messages);

        const stream = await this.client.chatStream({
          model: request.model || this.config.model,
          messages,
          maxTokens: this.normalizeMaxTokens(
            request.maxTokens || this.config.maxTokens,
          ),
          temperature: this.normalizeTemperature(
            request.temperature || this.config.temperature,
          ),
          p: request.topP || this.config.topP,
          stopSequences: request.stop,
        });

        return this.convertStreamResponse(stream as any);
      } catch (error) {
        throw this.handleCohereError(error);
      }
    });
  }

  async countTokens(content: string): Promise<number> {
    try {
      const response = await this.client.tokenize({
        text: content,
        model: this.config.model,
      });
      return response.tokens?.length || Math.ceil(content.length / 4);
    } catch (_error) {
      // Fallback to estimation if tokenize fails
      return Math.ceil(content.length / 4);
    }
  }

  /**
   * Convert internal messages to Cohere V2 format
   */
  private convertMessagesV2(
    messages: ProviderMessage[],
  ): Array<{ role: string; content: string }> {
    return messages.map((msg) => {
      const content =
        typeof msg.content === 'string'
          ? msg.content
          : msg.content
              .map((part) => ('text' in part ? part.text || '' : ''))
              .join('');

      return {
        role:
          msg.role === 'assistant'
            ? 'assistant'
            : msg.role === 'system'
              ? 'system'
              : 'user',
        content,
      };
    });
  }

  /**
   * Convert Cohere response to internal format
   */
  private convertResponse(response: {
    text?: string;
    meta?: { billedUnits?: { inputTokens?: number; outputTokens?: number } };
    finishReason?: string | null;
  }): ProviderResponse {
    const content = response.text || '';

    return {
      content,
      usage: response.meta?.billedUnits
        ? {
            promptTokens: response.meta.billedUnits.inputTokens || 0,
            completionTokens: response.meta.billedUnits.outputTokens || 0,
            totalTokens:
              (response.meta.billedUnits.inputTokens || 0) +
              (response.meta.billedUnits.outputTokens || 0),
          }
        : undefined,
      finishReason: this.convertFinishReason(response.finishReason),
      model: this.config.model,
    };
  }

  /**
   * Convert Cohere stream response to internal format
   */
  private async *convertStreamResponse(
    stream: AsyncIterable<{ eventType: string; text?: string; response?: any }>,
  ): AsyncGenerator<ProviderStreamChunk> {
    let fullContent = '';
    let usage:
      | { promptTokens: number; completionTokens: number; totalTokens: number }
      | undefined = undefined;

    for await (const event of stream) {
      if (event.eventType === 'text-generation') {
        const delta = event.text || '';
        fullContent += delta;

        yield {
          content: fullContent,
          delta,
          usage,
          finishReason: undefined,
        };
      } else if (event.eventType === 'stream-end') {
        if (event.response?.meta?.billedUnits) {
          usage = {
            promptTokens: event.response.meta.billedUnits.inputTokens || 0,
            completionTokens: event.response.meta.billedUnits.outputTokens || 0,
            totalTokens:
              (event.response.meta.billedUnits.inputTokens || 0) +
              (event.response.meta.billedUnits.outputTokens || 0),
          };
        }

        yield {
          content: fullContent,
          delta: '',
          usage,
          finishReason: this.convertFinishReason(event.response?.finishReason),
        };
      }
    }
  }

  /**
   * Convert Cohere finish reason to internal format
   */
  private convertFinishReason(
    finishReason: string | null,
  ): 'stop' | 'length' | 'content_filter' | 'tool_calls' | undefined {
    switch (finishReason) {
      case 'COMPLETE':
        return 'stop';
      case 'MAX_TOKENS':
        return 'length';
      case 'ERROR':
        return 'content_filter';
      case 'ERROR_TOXIC':
        return 'content_filter';
      default:
        return undefined;
    }
  }

  /**
   * Handle Cohere-specific errors
   */
  private handleCohereError(error: unknown): ProviderError {
    const message = (error as Error).message || String(error);
    const statusCode =
      (error as { statusCode?: number; status?: number }).statusCode ||
      (error as { statusCode?: number; status?: number }).status;

    if (
      statusCode === 401 ||
      message.includes('unauthorized') ||
      message.includes('invalid api key')
    ) {
      return new ProviderError(
        ProviderErrorType.AUTHENTICATION,
        'Invalid Cohere API key',
        statusCode,
      );
    }

    if (
      statusCode === 429 ||
      message.includes('rate limit') ||
      message.includes('too many requests')
    ) {
      return new ProviderError(
        ProviderErrorType.RATE_LIMIT,
        'Cohere rate limit exceeded',
        statusCode,
        this.extractRetryAfter(error),
      );
    }

    if (
      statusCode === 402 ||
      message.includes('quota') ||
      message.includes('billing')
    ) {
      return new ProviderError(
        ProviderErrorType.QUOTA_EXCEEDED,
        'Cohere quota exceeded',
        statusCode,
      );
    }

    if (statusCode === 400 || message.includes('invalid request')) {
      return new ProviderError(
        ProviderErrorType.INVALID_REQUEST,
        `Cohere API error: ${message}`,
        statusCode,
      );
    }

    if (statusCode === 404 || message.includes('not found')) {
      return new ProviderError(
        ProviderErrorType.MODEL_NOT_FOUND,
        `Cohere model not found: ${message}`,
        statusCode,
      );
    }

    return this.handleError(error);
  }
}

// Register the provider
ProviderFactory.registerProvider(ProviderType.COHERE, CohereProvider);