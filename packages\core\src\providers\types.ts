/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { Part } from '@google/genai';

/**
 * Supported AI providers
 */
export enum ProviderType {
  GEMINI = 'gemini',
  OPENAI = 'openai',
  ANTHROPIC = 'anthropic',
  COHERE = 'cohere',
  MISTRAL = 'mistral',
  OPENAI_COMPATIBLE = 'openai-compatible',
}

/**
 * Common message format for all providers
 */
export interface ProviderMessage {
  role: 'user' | 'assistant' | 'system';
  content: string | Part[];
  name?: string;
}

/**
 * Common response format for all providers
 */
export interface ProviderResponse {
  content: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  finishReason?: 'stop' | 'length' | 'content_filter' | 'tool_calls';
  model?: string;
}

/**
 * Streaming response chunk
 */
export interface ProviderStreamChunk {
  content: string;
  delta: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  finishReason?: 'stop' | 'length' | 'content_filter' | 'tool_calls';
}

/**
 * Provider configuration
 */
export interface ProviderConfig {
  provider: ProviderType;
  apiKey?: string;
  model: string;
  baseUrl?: string; // For OpenAI-compatible APIs
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  timeout?: number;
  proxy?: string;
  // Provider-specific options
}

/**
 * Provider capabilities
 */
export interface ProviderCapabilities {
  supportsStreaming: boolean;
  supportsTools: boolean;
  supportsImages: boolean;
  supportsSystemMessages: boolean;
  maxContextLength: number;
  supportedModels: string[];
}

/**
 * Error types for providers
 */
export enum ProviderErrorType {
  AUTHENTICATION = 'authentication',
  RATE_LIMIT = 'rate_limit',
  QUOTA_EXCEEDED = 'quota_exceeded',
  INVALID_REQUEST = 'invalid_request',
  MODEL_NOT_FOUND = 'model_not_found',
  NETWORK_ERROR = 'network_error',
  UNKNOWN = 'unknown',
}

/**
 * Provider error
 */
export class ProviderError extends Error {
  constructor(
    public type: ProviderErrorType,
    message: string,
    public statusCode?: number,
    public retryAfter?: number,
  ) {
    super(message);
    this.name = 'ProviderError';
  }
}

/**
 * Request parameters for generating content
 */
export interface GenerateContentRequest {
  messages: ProviderMessage[];
  model?: string;
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stop?: string[];
}

/**
 * Model information
 */
export interface ModelInfo {
  id: string;
  name: string;
  description?: string;
  contextLength: number;
  inputCostPer1kTokens?: number;
  outputCostPer1kTokens?: number;
  supportsTools: boolean;
  supportsImages: boolean;
}

/**
 * Provider model mappings
 */
export const PROVIDER_MODELS: Record<ProviderType, ModelInfo[]> = {
  [ProviderType.GEMINI]: [
    {
      id: 'gemini-2.5-pro',
      name: 'Gemini 2.5 Pro',
      contextLength: 2000000,
      supportsTools: true,
      supportsImages: true,
    },
    {
      id: 'gemini-2.5-flash',
      name: 'Gemini 2.5 Flash',
      contextLength: 1000000,
      supportsTools: true,
      supportsImages: true,
    },
  ],
  [ProviderType.OPENAI]: [
    {
      id: 'gpt-4',
      name: 'GPT-4',
      contextLength: 8192,
      supportsTools: true,
      supportsImages: false,
    },
    {
      id: 'gpt-4-turbo',
      name: 'GPT-4 Turbo',
      contextLength: 128000,
      supportsTools: true,
      supportsImages: true,
    },
    {
      id: 'gpt-3.5-turbo',
      name: 'GPT-3.5 Turbo',
      contextLength: 16385,
      supportsTools: true,
      supportsImages: false,
    },
  ],
  [ProviderType.ANTHROPIC]: [
    {
      id: 'claude-3-opus-20240229',
      name: 'Claude 3 Opus',
      contextLength: 200000,
      supportsTools: true,
      supportsImages: true,
    },
    {
      id: 'claude-3-sonnet-20240229',
      name: 'Claude 3 Sonnet',
      contextLength: 200000,
      supportsTools: true,
      supportsImages: true,
    },
    {
      id: 'claude-3-haiku-20240307',
      name: 'Claude 3 Haiku',
      contextLength: 200000,
      supportsTools: true,
      supportsImages: true,
    },
  ],
  [ProviderType.COHERE]: [
    {
      id: 'command-r',
      name: 'Command R',
      contextLength: 128000,
      supportsTools: true,
      supportsImages: false,
    },
    {
      id: 'command-r-plus',
      name: 'Command R+',
      contextLength: 128000,
      supportsTools: true,
      supportsImages: false,
    },
  ],
  [ProviderType.MISTRAL]: [
    {
      id: 'mistral-large-latest',
      name: 'Mistral Large',
      contextLength: 128000,
      supportsTools: true,
      supportsImages: false,
    },
    {
      id: 'mistral-medium-latest',
      name: 'Mistral Medium',
      contextLength: 32000,
      supportsTools: false,
      supportsImages: false,
    },
  ],
  [ProviderType.OPENAI_COMPATIBLE]: [], // Will be populated dynamically
};

/**
 * Default models for each provider
 */
export const DEFAULT_MODELS: Record<ProviderType, string> = {
  [ProviderType.GEMINI]: 'gemini-2.5-pro',
  [ProviderType.OPENAI]: 'gpt-4-turbo',
  [ProviderType.ANTHROPIC]: 'claude-3-sonnet-20240229',
  [ProviderType.COHERE]: 'command-r-plus',
  [ProviderType.MISTRAL]: 'mistral-large-latest',
  [ProviderType.OPENAI_COMPATIBLE]: 'gpt-3.5-turbo', // Default fallback
};
