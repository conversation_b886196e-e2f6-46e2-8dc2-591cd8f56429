/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import {
  ProviderConfig,
  ProviderResponse,
  ProviderStreamChunk,
  GenerateContentRequest,
  ProviderCapabilities,
  ProviderError,
  ProviderErrorType,
  ModelInfo,
} from './types.js';

/**
 * Abstract base class for all AI providers
 */
export abstract class BaseProvider {
  protected config: ProviderConfig;

  constructor(config: ProviderConfig) {
    this.config = config;
    this.validateConfig();
  }

  /**
   * Validate provider configuration
   */
  protected validateConfig(): void {
    if (!this.config.apiKey && this.requiresApiKey()) {
      throw new ProviderError(
        ProviderErrorType.AUTHENTICATION,
        `API key is required for ${this.config.provider} provider`,
      );
    }

    if (!this.config.model) {
      throw new ProviderError(
        ProviderErrorType.INVALID_REQUEST,
        'Model is required',
      );
    }

    const supportedModels = this.getSupportedModels().map((m) => m.id);
    if (!supportedModels.includes(this.config.model)) {
      throw new ProviderError(
        ProviderErrorType.MODEL_NOT_FOUND,
        `Model ${this.config.model} is not supported by ${this.config.provider} provider. Supported models: ${supportedModels.join(', ')}`,
      );
    }
  }

  /**
   * Whether this provider requires an API key
   */
  protected abstract requiresApiKey(): boolean;

  /**
   * Get provider capabilities
   */
  abstract getCapabilities(): ProviderCapabilities;

  /**
   * Get supported models for this provider
   */
  abstract getSupportedModels(): ModelInfo[];

  /**
   * Generate content using the provider
   */
  abstract generateContent(
    request: GenerateContentRequest,
  ): Promise<ProviderResponse>;

  /**
   * Generate content with streaming
   */
  abstract generateContentStream(
    request: GenerateContentRequest,
  ): Promise<AsyncGenerator<ProviderStreamChunk>>;

  /**
   * Count tokens for the given content
   */
  abstract countTokens(content: string): Promise<number>;

  /**
   * Get the current model being used
   */
  getModel(): string {
    return this.config.model;
  }

  /**
   * Get the provider type
   */
  getProviderType(): string {
    return this.config.provider;
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<ProviderConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.validateConfig();
  }

  /**
   * Handle provider-specific errors
   */
  protected handleError(error: unknown): ProviderError {
    // Default error handling - subclasses should override for provider-specific logic
    if (error instanceof ProviderError) {
      return error;
    }

    // Try to determine error type from common patterns
    const message = error.message || error.toString();
    const statusCode = error.status || error.statusCode;

    if (
      statusCode === 401 ||
      message.includes('unauthorized') ||
      message.includes('invalid api key')
    ) {
      return new ProviderError(
        ProviderErrorType.AUTHENTICATION,
        'Authentication failed. Please check your API key.',
        statusCode,
      );
    }

    if (
      statusCode === 429 ||
      message.includes('rate limit') ||
      message.includes('too many requests')
    ) {
      const retryAfter = error.retryAfter || this.extractRetryAfter(error);
      return new ProviderError(
        ProviderErrorType.RATE_LIMIT,
        'Rate limit exceeded. Please try again later.',
        statusCode,
        retryAfter,
      );
    }

    if (
      statusCode === 402 ||
      message.includes('quota') ||
      message.includes('billing')
    ) {
      return new ProviderError(
        ProviderErrorType.QUOTA_EXCEEDED,
        'Quota exceeded. Please check your billing and usage limits.',
        statusCode,
      );
    }

    if (statusCode === 400 || message.includes('invalid request')) {
      return new ProviderError(
        ProviderErrorType.INVALID_REQUEST,
        `Invalid request: ${message}`,
        statusCode,
      );
    }

    if (statusCode === 404 || message.includes('not found')) {
      return new ProviderError(
        ProviderErrorType.MODEL_NOT_FOUND,
        `Model or endpoint not found: ${message}`,
        statusCode,
      );
    }

    if (
      statusCode >= 500 ||
      message.includes('network') ||
      message.includes('timeout')
    ) {
      return new ProviderError(
        ProviderErrorType.NETWORK_ERROR,
        `Network error: ${message}`,
        statusCode,
      );
    }

    return new ProviderError(
      ProviderErrorType.UNKNOWN,
      `Unknown error: ${message}`,
      statusCode,
    );
  }

  /**
   * Extract retry-after value from error
   */
  protected extractRetryAfter(error: unknown): number | undefined {
    // Try to extract from headers
    if (error.headers && error.headers['retry-after']) {
      return parseInt(error.headers['retry-after'], 10);
    }

    // Try to extract from response
    if (
      error.response &&
      error.response.headers &&
      error.response.headers['retry-after']
    ) {
      return parseInt(error.response.headers['retry-after'], 10);
    }

    return undefined;
  }

  /**
   * Apply rate limiting with exponential backoff
   */
  protected async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000,
  ): Promise<T> {
    let lastError: ProviderError;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = this.handleError(error);

        // Don't retry on authentication or invalid request errors
        if (
          lastError.type === ProviderErrorType.AUTHENTICATION ||
          lastError.type === ProviderErrorType.INVALID_REQUEST ||
          lastError.type === ProviderErrorType.MODEL_NOT_FOUND
        ) {
          throw lastError;
        }

        // Don't retry on the last attempt
        if (attempt === maxRetries) {
          throw lastError;
        }

        // Calculate delay with exponential backoff
        let delay = baseDelay * Math.pow(2, attempt);

        // Use retry-after header if available
        if (lastError.retryAfter) {
          delay = lastError.retryAfter * 1000;
        }

        // Add jitter to prevent thundering herd
        delay += Math.random() * 1000;

        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }

  /**
   * Normalize temperature value for the provider
   */
  protected normalizeTemperature(temperature?: number): number | undefined {
    if (temperature === undefined) return undefined;
    return Math.max(0, Math.min(1, temperature));
  }

  /**
   * Normalize max tokens value for the provider
   */
  protected normalizeMaxTokens(maxTokens?: number): number | undefined {
    if (maxTokens === undefined) return undefined;
    const capabilities = this.getCapabilities();
    return Math.min(maxTokens, capabilities.maxContextLength);
  }
}
