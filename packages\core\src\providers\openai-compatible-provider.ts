/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import OpenAI from 'openai';
import { BaseProvider } from './base-provider.js';
import {
  ProviderConfig,
  ProviderResponse,
  ProviderStreamChunk,
  GenerateContentRequest,
  ProviderCapabilities,
  ProviderMessage,
  ProviderError,
  ProviderErrorType,
  ModelInfo,
  ProviderType,
} from './types.js';
import { ProviderFactory } from './provider-factory.js';

/**
 * OpenAI-compatible provider implementation
 * Supports Ollama, LocalAI, LM Studio, and other OpenAI-compatible APIs
 */
export class OpenAICompatibleProvider extends BaseProvider {
  private client: OpenAI;

  constructor(config: ProviderConfig) {
    super(config);

    if (!config.baseUrl) {
      throw new ProviderError(
        ProviderErrorType.INVALID_REQUEST,
        'Base URL is required for OpenAI-compatible provider. Please set S647_BASE_URL in your .env file.',
      );
    }

    this.client = new OpenAI({
      apiKey: config.apiKey || 'dummy-key', // Some local APIs don't require real keys
      baseURL: config.baseUrl,
      timeout: config.timeout || 30000,
    });
  }

  protected requiresApiKey(): boolean {
    return false; // Many local APIs don't require API keys
  }

  getCapabilities(): ProviderCapabilities {
    return {
      supportsStreaming: true,
      supportsTools: false, // Most local models don't support tools yet
      supportsImages: false, // Most local models don't support images yet
      supportsSystemMessages: true,
      maxContextLength: 32000, // Conservative default
      supportedModels: [], // Will be populated dynamically
    };
  }

  getSupportedModels(): ModelInfo[] {
    // For OpenAI-compatible APIs, models are usually discovered dynamically
    // Return empty array as models depend on the specific deployment
    return [];
  }

  async generateContent(
    request: GenerateContentRequest,
  ): Promise<ProviderResponse> {
    return this.withRetry(async () => {
      try {
        const messages = this.convertMessages(request.messages);

        const response = await this.client.chat.completions.create({
          model: request.model || this.config.model,
          messages,
          max_tokens: this.normalizeMaxTokens(
            request.maxTokens || this.config.maxTokens,
          ),
          temperature: this.normalizeTemperature(
            request.temperature || this.config.temperature,
          ),
          top_p: request.topP || this.config.topP,
          frequency_penalty:
            request.frequencyPenalty || this.config.frequencyPenalty,
          presence_penalty:
            request.presencePenalty || this.config.presencePenalty,
          stop: request.stop,
          stream: false,
        });

        return this.convertResponse(response);
      } catch (error) {
        throw this.handleOpenAICompatibleError(error);
      }
    });
  }

  async generateContentStream(
    request: GenerateContentRequest,
  ): Promise<AsyncGenerator<ProviderStreamChunk>> {
    return this.withRetry(async () => {
      try {
        const messages = this.convertMessages(request.messages);

        const stream = await this.client.chat.completions.create({
          model: request.model || this.config.model,
          messages,
          max_tokens: this.normalizeMaxTokens(
            request.maxTokens || this.config.maxTokens,
          ),
          temperature: this.normalizeTemperature(
            request.temperature || this.config.temperature,
          ),
          top_p: request.topP || this.config.topP,
          frequency_penalty:
            request.frequencyPenalty || this.config.frequencyPenalty,
          presence_penalty:
            request.presencePenalty || this.config.presencePenalty,
          stop: request.stop,
          stream: true,
        });

        return this.convertStreamResponse(stream);
      } catch (error) {
        throw this.handleOpenAICompatibleError(error);
      }
    });
  }

  async countTokens(content: string): Promise<number> {
    // Most OpenAI-compatible APIs don't have token counting
    // Use a rough estimation: ~4 characters per token
    return Math.ceil(content.length / 4);
  }

  /**
   * Convert internal messages to OpenAI format
   */
  private convertMessages(
    messages: ProviderMessage[],
  ): OpenAI.Chat.Completions.ChatCompletionMessageParam[] {
    return messages.map((msg) => {
      if (typeof msg.content === 'string') {
        return {
          role: msg.role,
          content: msg.content,
          ...(msg.name && { name: msg.name }),
        } as OpenAI.Chat.Completions.ChatCompletionMessageParam;
      } else {
        // Most local models don't support multimodal content yet
        // Convert to text only
        const text = msg.content
          .map((part) =>
            'text' in part ? part.text || '' : '[Unsupported content type]',
          )
          .join('');

        return {
          role: msg.role,
          content: text,
          ...(msg.name && { name: msg.name }),
        } as OpenAI.Chat.Completions.ChatCompletionMessageParam;
      }
    });
  }

  /**
   * Convert OpenAI-compatible response to internal format
   */
  private convertResponse(
    response: OpenAI.Chat.Completions.ChatCompletion,
  ): ProviderResponse {
    const choice = response.choices[0];
    const content = choice.message.content || '';

    return {
      content,
      usage: response.usage
        ? {
            promptTokens: response.usage.prompt_tokens,
            completionTokens: response.usage.completion_tokens,
            totalTokens: response.usage.total_tokens,
          }
        : undefined,
      finishReason: this.convertFinishReason(choice.finish_reason),
      model: response.model,
    };
  }

  /**
   * Convert OpenAI-compatible stream response to internal format
   */
  private async *convertStreamResponse(
    stream: AsyncIterable<OpenAI.Chat.Completions.ChatCompletionChunk>,
  ): AsyncGenerator<ProviderStreamChunk> {
    let fullContent = '';

    for await (const chunk of stream) {
      const choice = chunk.choices[0];
      if (!choice) continue;

      const delta = choice.delta.content || '';
      fullContent += delta;

      yield {
        content: fullContent,
        delta,
        usage: chunk.usage
          ? {
              promptTokens: chunk.usage.prompt_tokens,
              completionTokens: chunk.usage.completion_tokens,
              totalTokens: chunk.usage.total_tokens,
            }
          : undefined,
        finishReason: this.convertFinishReason(choice.finish_reason),
      };
    }
  }

  /**
   * Convert OpenAI-compatible finish reason to internal format
   */
  private convertFinishReason(
    finishReason: string | null,
  ): 'stop' | 'length' | 'content_filter' | 'tool_calls' | undefined {
    switch (finishReason) {
      case 'stop':
        return 'stop';
      case 'length':
        return 'length';
      case 'content_filter':
        return 'content_filter';
      case 'tool_calls':
        return 'tool_calls';
      default:
        return undefined;
    }
  }

  /**
   * Handle OpenAI-compatible API errors
   */
  private handleOpenAICompatibleError(error: unknown): ProviderError {
    const message = error.message || error.toString();
    const statusCode = error.status || error.statusCode;

    // Connection errors (common with local APIs)
    if (
      message.includes('ECONNREFUSED') ||
      message.includes('ENOTFOUND') ||
      message.includes('ETIMEDOUT')
    ) {
      return new ProviderError(
        ProviderErrorType.NETWORK_ERROR,
        `Cannot connect to OpenAI-compatible API at ${this.config.baseUrl}. Make sure the service is running.`,
        statusCode,
      );
    }

    if (statusCode === 401 || message.includes('unauthorized')) {
      return new ProviderError(
        ProviderErrorType.AUTHENTICATION,
        'Authentication failed with OpenAI-compatible API',
        statusCode,
      );
    }

    if (statusCode === 429 || message.includes('rate limit')) {
      return new ProviderError(
        ProviderErrorType.RATE_LIMIT,
        'Rate limit exceeded on OpenAI-compatible API',
        statusCode,
        this.extractRetryAfter(error),
      );
    }

    if (statusCode === 400 || message.includes('invalid request')) {
      return new ProviderError(
        ProviderErrorType.INVALID_REQUEST,
        `OpenAI-compatible API error: ${message}`,
        statusCode,
      );
    }

    if (statusCode === 404 || message.includes('not found')) {
      return new ProviderError(
        ProviderErrorType.MODEL_NOT_FOUND,
        `Model not found on OpenAI-compatible API: ${message}`,
        statusCode,
      );
    }

    return this.handleError(error);
  }

  /**
   * Get available models from the API (if supported)
   */
  async getAvailableModels(): Promise<string[]> {
    try {
      const response = await this.client.models.list();
      return response.data.map((model) => model.id);
    } catch (error) {
      // Many local APIs don't support model listing
      const message = error instanceof Error ? error.message : String(error);
      console.warn(
        'Could not fetch available models from OpenAI-compatible API:',
        message,
      );
      return [];
    }
  }
}

// Register the provider
ProviderFactory.registerProvider(
  ProviderType.OPENAI_COMPATIBLE,
  OpenAICompatibleProvider,
);
